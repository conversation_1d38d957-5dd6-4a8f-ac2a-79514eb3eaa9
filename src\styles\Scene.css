/* Scene页面样式 */

/* 主题色彩变量 */
:root {
  --primary-color: #21808d;
  --primary-color-medium: rgba(33, 128, 141, 0.65);
  --primary-color-light: rgba(33, 128, 141, 0.15);
  --text-primary: #262626;
  --text-secondary: #8c8c8c;
  --border-color: #f0f0f0;
  --background-light: #fafafa;
  --white: #fff;
  --shadow-light: 2px 2px 8px rgba(0, 0, 0, 0.15);
}

/* 通用按钮样式 */
.scene-button {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-button:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 主标题样式 */
.scene-title {
  margin: 0;
  color: var(--text-primary);
  font-size: 28px !important;
  font-weight: 600;
}

/* 状态标签样式 */
.status-tag {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.status-tag-pending {
  color: var(--text-secondary);
}

.status-tag-in-progress {
  color: var(--primary-color);
}

.status-tag-submitted {
  color: #13343b;
}

/* 老师信息样式 */
.teacher-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 6px;
  object-fit: cover;
}

.teacher-icon {
  margin-right: 6px;
  color: var(--text-secondary);
  font-size: 16px;
}

.teacher-text {
  font-size: 14px;
}

/* 信息文本样式 */
.info-text {
  font-size: 14px;
}

.info-icon {
  margin-right: 6px;
  color: var(--text-secondary);
  font-size: 16px;
}

/* Scene 布局样式 */
.scene-header {
  margin-bottom: 0;
}

.scene-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.scene-header-info {
  margin-bottom: 16px;
}

.scene-header-meta {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

/* 加载状态样式 */
.scene-loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 60vh;
}

.scene-loading-text {
  margin-top: 16px;
}

/* 场景布局样式 */
.scene-layout {
  min-height: 70vh;
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

/* 强制显示分隔线 - 覆盖所有可能的Antd样式 */
.scene-layout .ant-layout-sider::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: var(--border-color);
  z-index: 10;
}

/* 折叠按钮样式 - 已移动到下方统一定义 */

/* 角色列表样式 */
.scene-character-sider {
  background: var(--background-light);
  border-right: 1px solid var(--border-color) !important;
  transition: width 0.25s ease-out !important;
  overflow: hidden !important;
  position: relative;
}

/* 确保折叠状态下仍然显示分隔线 */
.scene-character-sider.ant-layout-sider-collapsed {
  border-right: 1px solid var(--border-color) !important;
  background: var(--background-light);
}

/* 完全重写折叠动画逻辑，彻底消除残影 */
.scene-character-sider .ant-layout-sider-children {
  position: absolute;
  top: 0;
  left: 0;
  width: 240px;
  height: 100%;
  background: var(--background-light);
  transition: transform 0.05s ease-out, opacity 0.2s ease-out !important;
  transform-origin: left center;
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.scene-character-sider.ant-layout-sider-collapsed .ant-layout-sider-children {
  transform: translateX(-100%);
  opacity: 0;
  pointer-events: none;
}

.scene-character-sider:not(.ant-layout-sider-collapsed) .ant-layout-sider-children {
  transform: translateX(0);
  opacity: 1;
  pointer-events: auto;
}

/* 覆盖Antd默认的Sider动画，使用更精确的控制 */
.scene-layout .ant-layout-sider {
  transition: width 0.25s ease-out !important;
  overflow: visible !important;
}

.scene-layout .ant-layout-sider-trigger {
  display: none !important;
}

/* 确保折叠状态下内容完全不可见，但保持分隔线 */
.scene-character-sider.ant-layout-sider-collapsed .scene-character-list-container,
.scene-character-sider.ant-layout-sider-collapsed .scene-character-header,
.scene-character-sider.ant-layout-sider-collapsed .scene-character-item {
  visibility: hidden;
  opacity: 0;
}

.scene-character-list-container {
  padding: 0;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: var(--background-light);
}

/* 角色列表顶部标题样式 */
.scene-character-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background: var(--white);
}

.scene-character-header-left {
  display: flex;
  align-items: center;
}

/* 参会者标题字体样式 - 与Worksheet.tsx中练习背景按钮样式一致 */
.scene-character-header-left span {
  padding: 8px 12px;
  height: auto;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--text-primary) !important;
  border-radius: 6px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent;
}

.scene-character-header-right {
  display: flex;
  align-items: center;
}

/* 参会者列表item样式 */
.scene-character-item {
  margin: 8px 8px !important;
  border-radius: 8px !important;
  height: auto !important;
  line-height: normal !important;
  padding: 12px !important;
  width: calc(100% - 16px) !important;
  transition: background-color 0.2s ease !important;
  border-bottom: none !important;
  background: transparent !important;
  position: relative;
  cursor: default;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.scene-character-item:hover {
  background: rgba(33, 128, 141, 0.15) !important;
}

.scene-character-item:last-child {
  border-bottom: none !important;
}

.scene-character-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.scene-character-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.scene-character-meta {
  display: flex;
  flex-direction: column;
  gap: 0;
  width: 100%;
}

.scene-character-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.scene-character-name-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 参会者姓名字体样式 - 与Worksheet.tsx中单元列表未选中的列表项字体样式一致 */
.scene-character-name-group span {
  font-weight: 500 !important;
  color: var(--text-primary) !important;
  font-size: 16px !important;
}

.scene-character-info-button {
  padding: 0 4px;
  height: auto;
  font-size: 14px;
  color: var(--primary-color);
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-character-info-button:hover {
  color: var(--primary-color) !important;
  background: transparent !important;
}

.scene-character-status-icon {
  display: flex;
  align-items: center;
}

/* 收起按钮样式 - 与Worksheet.tsx完全一致 */
.scene-character-fold-button {
  font-size: 16px;
  width: 28px;
  height: 28px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-character-fold-button:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 展开按钮样式 - 与Worksheet.tsx中展开按钮一致，靠近下半部分紧贴左侧边缘 */
.scene-unfold-button {
  position: absolute;
  top: 16px;
  left: 0px;
  z-index: 1000;
  background: var(--white);
  border: 1px solid #d9d9d9;
  border-radius: 0 6px 6px 0;
  font-size: 16px;
  width: 32px;
  height: 32px;
  box-shadow: var(--shadow-light);
  outline: none;
}

.scene-unfold-button:hover {
  border: 1px solid #d9d9d9 !important;
  outline: none !important;
  box-shadow: var(--shadow-light) !important;
}

.scene-unfold-button:focus {
  border: 1px solid #d9d9d9 !important;
  outline: none !important;
  box-shadow: var(--shadow-light) !important;
}

.scene-character-status-icon {
  display: flex;
  align-items: center;
}

/* 机器人/学员扮演图标样式 */
.scene-character-user-icon{
  font-size: 16px;
  color: var(--primary-color-medium) !important;
  transition: color 0.2s ease;
  padding: 4px;
}

.scene-character-robot-icon {
  font-size: 16px;
  color: var(--text-secondary) !important;
  transition: color 0.2s ease;
  padding: 4px;
}

.scene-character-user-icon:hover,
.scene-character-robot-icon:hover {
  color: #1a6b75 !important;
}

.scene-character-user-icon:focus,
.scene-character-robot-icon:focus {
  color: var(--primary-color) !important;
}

.scene-character-user-icon:active,
.scene-character-robot-icon:active {
  color: var(--primary-color) !important;
}

/* 聊天区域样式 */
.scene-chat-layout {
  display: flex;
  flex-direction: column;
}

.scene-chat-header {
  padding: 14px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 49px; /* 与左侧header高度对齐 */
}

.scene-chat-header-left {
  display: flex;
  align-items: center;
}

.scene-chat-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-fold-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自动发声开关样式 */
.scene-voice-switch-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 4px;
}

/* 自动发声icon样式 - 普通图标，无交互效果 */
.scene-voice-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 16px;
  cursor: default;
}

.scene-voice-switch {
  background-color: #f0f0f0 !important;
  font-size: 15px;
}

.scene-voice-switch.ant-switch-checked {
  background-color: var(--primary-color) !important;
}

.scene-voice-switch:focus {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.scene-voice-switch:focus-within {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.scene-voice-switch .ant-switch-handle::before {
  background-color: #fff !important;
}

/* 确保toggle控件内的文字大小与练习指南按钮一致 */
.scene-voice-switch .ant-switch-inner span {
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* 手动发声状态时的文字颜色与练习指南一致 */
.scene-voice-switch:not(.ant-switch-checked) .ant-switch-inner {
  background-color: var(--text-secondary) !important;
}

/* 练习指南按钮样式 - 与Worksheet.tsx中练习指南按钮样式一致 */
.scene-guide-button {
  margin: 0 4px;
  padding: 8px 12px;
  height: auto;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary) !important;
  border-radius: 6px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent;
  transition: all 0.3s ease;
}

.scene-guide-button .anticon {
  color: var(--primary-color) !important;
}

.scene-guide-button:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-guide-button:hover .anticon {
  color: var(--primary-color) !important;
}

.scene-guide-button:focus {
  background: transparent !important;
  color: var(--text-primary) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-guide-button:focus:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-guide-button:focus:hover .anticon {
  color: var(--primary-color) !important;
}

.scene-guide-button:active {
  background: transparent !important;
  color: var(--text-primary) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 聊天消息区域样式 */
.scene-chat-content {
  flex: 1;
  padding: 8px 12px;
  padding-bottom: 85px; /* 为悬浮输入控件预留合适空间 */
  overflow-y: auto;
  background: rgba(229, 231, 235, 0.01);
}

.scene-chat-empty {
  margin-top: 100px;
}

.scene-messages-container {
  max-width: 100%;
  margin: 0;
  padding: 8px; /* 添加左右内边距，为气泡框靠边留出空间 */
}

.scene-message-item {
  margin-bottom: 20px;
  width: 100%;
}

.scene-message-item.user {
  display: flex;
  justify-content: flex-end;
}

.scene-message-item.ai {
  display: flex;
  justify-content: flex-start;
}

.scene-message-wrapper {
  max-width: 90%;
  min-width: 200px;
  position: relative;
}

.scene-message-wrapper.user {
  margin-right: 0; /* 用户消息靠右边缘 */
}

.scene-message-wrapper.ai {
  margin-left: 0; /* AI消息靠左边缘 */
}

/* 消息头部样式 - 重构为两个控件的布局 */
.scene-message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  position: relative;
  width: 100%;
}

.scene-message-header.user {
  /* 用户消息：功能按钮在左，信息控件在右 */
  flex-direction: row;
}

.scene-message-header.ai {
  /* AI消息：信息控件在左，功能按钮在右 */
  flex-direction: row;
}

/* 功能按钮控件 */
.scene-message-actions-widget {
  display: flex;
  gap: 4px;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

/* 信息控件：包含头像、姓名、时间 */
.scene-message-info-widget {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-message-info-widget.user {
  flex-direction: row-reverse; /* 用户：头像在右，信息在左 */
}

.scene-message-info-widget.ai {
  flex-direction: row; /* AI：头像在左，信息在右 */
}

.scene-message-avatar {
  flex-shrink: 0;
}

.scene-message-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.scene-message-info.user {
  align-items: flex-end; /* 用户消息信息右对齐 */
}

.scene-message-info.ai {
  align-items: flex-start; /* AI消息信息左对齐 */
}

.scene-message-name {
  font-size: 14px; /* 修改为13px */
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.2;
}

.scene-message-time {
  font-size: 11px;
  color: var(--text-secondary);
  line-height: 1.2;
}

/* 消息气泡样式 */
.scene-message-bubble {
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: none;
  margin-bottom: 8px;
  word-wrap: break-word;
  word-break: break-word;
  font-size: 16px; /* 气泡内文字大小为16px */
}

.scene-message-bubble.user {
  background: var(--primary-color);
  color: #fff;
  border-top-right-radius: 4px; /* 右上角直角 */
}

.scene-message-bubble.ai {
  background: #fff;
  color: #000;
  border-top-left-radius: 4px; /* 左上角直角 */
}

/* Markdown内容样式 */
.scene-message-bubble .user-message-content {
  color: #fff !important;
}

.scene-message-bubble .user-message-content * {
  color: #fff !important;
}

.scene-message-bubble .user-message-content p {
  margin: 0 !important;
  line-height: 1.5 !important;
}

.scene-message-bubble .user-message-content pre {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 6px !important;
  margin: 8px 0 !important;
}

.scene-message-bubble .user-message-content code {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
}

.scene-message-bubble .ai-message-content {
  color: #000 !important;
}

.scene-message-bubble .ai-message-content * {
  color: #000 !important;
}

.scene-message-bubble .ai-message-content p {
  margin: 0 !important;
  line-height: 1.5 !important;
}

.scene-message-bubble .ai-message-content pre {
  background: #f6f8fa !important;
  border: 1px solid #e1e4e8 !important;
  border-radius: 6px !important;
  margin: 8px 0 !important;
}

.scene-message-bubble .ai-message-content code {
  background: #f6f8fa !important;
  color: #000 !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
}

.scene-message-bubble .ai-message-content blockquote {
  border-left: 4px solid #dfe2e5 !important;
  padding-left: 16px !important;
  margin: 8px 0 !important;
  color: #6a737d !important;
}

.scene-message-bubble .ai-message-content ul,
.scene-message-bubble .ai-message-content ol {
  margin: 8px 0 !important;
  padding-left: 20px !important;
}

.scene-message-bubble .ai-message-content li {
  margin: 4px 0 !important;
}

/* 鼠标悬浮时显示功能按钮 */
.scene-message-wrapper:hover .scene-message-actions-widget {
  opacity: 1;
  pointer-events: auto;
}

.scene-message-action-btn {
  height: 24px !important;
  width: 24px !important;
  padding: 0 !important;
  font-size: 14px !important;
  border-radius: 50% !important; /* 圆形背景 */
  color: var(--text-secondary) !important;
  background: rgba(229, 231, 235, 0.01) !important; /* 与聊天历史背景颜色一致 */
  border: none !important;
  outline: none !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.scene-message-action-btn:hover {
  color: var(--primary-color) !important;
  background: var(--primary-color-light) !important;
  border: none !important;
  outline: none !important;
}

.scene-message-action-btn:focus {
  color: var(--primary-color) !important;
  background: var(--primary-color-light) !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.scene-message-action-btn:active {
  color: var(--primary-color) !important;
  background: var(--primary-color-light) !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.scene-message-delete-btn:hover {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
}

.scene-message-delete-btn:focus {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
}

/* TTS发声按钮样式 */
.scene-message-tts-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* 鼠标悬浮时显示TTS按钮 */
.scene-message-wrapper:hover .scene-message-tts-btn {
  opacity: 1;
}

/* TTS播放状态样式 */
.scene-message-action-btn.tts-playing {
  color: #52c41a !important;
  background: rgba(82, 196, 26, 0.1) !important;
  opacity: 1 !important;
}

.scene-message-action-btn.tts-connecting {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
  opacity: 1 !important;
}

/* TTS图标动画效果 */
.tts-playing-icon {
  animation: tts-pulse 1.5s ease-in-out infinite;
}

.tts-connecting-icon {
  animation: tts-spin 1s linear infinite;
}

@keyframes tts-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes tts-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* TTS声纹效果样式 */
.tts-voice-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
  gap: 6px;
}

.voice-wave {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  height: 16px;
}

.wave-bar {
  width: 2px;
  background: #1890ff;
  border-radius: 1px;
  animation: wave-animation 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(1) {
  height: 8px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 12px;
  animation-delay: 0.1s;
}

.wave-bar:nth-child(3) {
  height: 10px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(4) {
  height: 6px;
  animation-delay: 0.3s;
}

@keyframes wave-animation {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* TTS结束按钮样式 */
.tts-stop-btn {
  height: 16px !important;
  width: 16px !important;
  padding: 0 !important;
  font-size: 10px !important;
  border-radius: 50% !important;
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.1) !important;
  border: none !important;
  outline: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 16px !important;
}

.tts-stop-btn:hover {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.2) !important;
  border: none !important;
  outline: none !important;
}

.tts-stop-btn:focus {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.2) !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.tts-stop-btn:active {
  color: #ff4d4f !important;
  background: rgba(255, 77, 79, 0.2) !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

/* 流式输入光标动画 */
.streaming-cursor {
  animation: blink 1s infinite;
  color: var(--primary-color);
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 底部输入区域样式 - 悬浮设计 */
.scene-input-floating-container {
  position: absolute;
  bottom: 8px; /* 减少底部距离，使输入控件更靠近聊天区域底部 */
  left: 50%;
  transform: translateX(-50%);
  width: 97%;
  z-index: 100;
}

.scene-input-area {
  background: #fff;
  border-radius: 12px;
  box-shadow: none;
  border: 1px solid #e8e8e8;
  min-height: 80px;
  max-height: 180px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加平滑的高度过渡 */
}

/* 拖拽指示器样式 */
.scene-drag-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  cursor: ns-resize;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  transition: background-color 0.2s;
}

.scene-drag-indicator:hover {
  background-color: var(--primary-color-light) !important;
}

.scene-drag-dots {
  display: flex;
  gap: 4px;
  transition: opacity 0.2s;
}

.scene-drag-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* Textarea容器 */
.scene-textarea-container {
  flex: 1;
  padding: 8px 16px 0 16px;
  background: #fff;
  position: relative;
  overflow: hidden; /* 容器隐藏溢出，让textarea内部处理滚动 */
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加平滑的高度过渡 */
  display: flex; /* 添加flex布局 */
  flex-direction: column; /* 垂直布局 */
  margin-bottom: 40px; /* 为function bar留出空间 */
}

.scene-input-textarea {
  width: 100% !important;
  height: 100% !important; /* 确保占满容器高度 */
  min-height: 28px !important; /* 设置最小高度 */
  max-height: none !important; /* 移除最大高度限制 */
  font-size: 15px !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 4px 8px !important; /* 减少顶部和底部padding */
  line-height: 1.4 !important; /* 减少行高 */
  overflow-y: auto !important; /* 确保可以垂直滚动 */
  overflow-x: hidden !important; /* 隐藏水平滚动 */
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
  will-change: auto; /* 优化渲染性能 */
  resize: none !important; /* 禁止手动调整大小 */
  box-sizing: border-box;
  color: var(--text-primary);
  font-family: inherit;
  flex: 1; /* 占满剩余空间 */
}

.scene-input-textarea::-webkit-scrollbar {
  width: 6px;
}

.scene-input-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.scene-input-textarea::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.scene-input-textarea::-webkit-scrollbar-thumb:hover {
  background-color: #bfbfbf;
}

.scene-input-textarea:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.scene-input-textarea::placeholder {
  color: #bfbfbf;
}

/* Function Bar */
.scene-function-bar {
  height: 40px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  flex-shrink: 0; /* 防止被压缩 */
  will-change: auto; /* 优化渲染性能 */
}

.scene-function-bar-left {
  display: flex;
  gap: 8px;
  align-items: center;
}

.scene-function-bar-right {
  display: flex;
  align-items: center;
}

.scene-function-button {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important; /* 改为圆形 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #8c8c8c !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.2s !important;
}

.scene-function-button:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
}

.scene-function-button:focus {
  background: transparent !important;
  color: #8c8c8c !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.scene-function-button:focus:hover {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-function-button:active {
  background: var(--primary-color-light) !important;
  color: var(--primary-color) !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  transform: none !important;
}

.scene-function-button:disabled {
  color: #d9d9d9 !important;
  background: transparent !important;
  cursor: not-allowed !important;
}

.scene-function-button:disabled:hover {
  color: #d9d9d9 !important;
  background: transparent !important;
}

/* 语音识别按钮特殊状态 */
.speech-recognition-button {
  position: relative;
  overflow: hidden;
}

/* 语音识别按钮默认状态的图标颜色 */
.speech-recognition-button .anticon {
  color: #8c8c8c !important;
}

/* 语音识别按钮默认状态hover时的图标颜色 - 与指定发言按钮一致 */
.speech-recognition-button:hover .anticon {
  color: var(--primary-color) !important;
}

/* 准备中状态 - 浅红色背景 */
.speech-recognition-button.preparing {
  background: rgba(255, 77, 79, 0.1) !important;
  color: #ff4d4f !important;
}

/* 准备中状态的图标颜色 */
.speech-recognition-button.preparing .anticon {
  color: #ff4d4f !important;
}

/* 识别中状态 - 红色背景 */
.speech-recognition-button.listening {
  background: #ff4d4f !important;
  color: #fff !important;
}

/* 识别中状态的图标颜色 */
.speech-recognition-button.listening .anticon {
  color: #fff !important;
}

/* 语音识别按钮在识别中状态下的hover样式 - 保持不变 */
.speech-recognition-button.preparing:hover,
.speech-recognition-button.listening:hover {
  background: rgba(255, 77, 79, 0.1) !important;
  color: #ff4d4f !important;
}

.speech-recognition-button.listening:hover {
  background: #ff4d4f !important;
  color: #fff !important;
}

/* 识别中状态hover时图标颜色保持不变 */
.speech-recognition-button.preparing:hover .anticon {
  color: #ff4d4f !important;
}

.speech-recognition-button.listening:hover .anticon {
  color: #fff !important;
}

/* 旋转光圈动画 */
@keyframes spin-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.speech-recognition-button.preparing::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-top: 2px solid #ff4d4f;
  border-radius: 50%;
  animation: spin-ring 1s linear infinite;
  z-index: 0;
}

.speech-recognition-button .speech-icon {
  position: relative;
  z-index: 1;
}

.scene-send-button {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 0px 16px !important;
  height: 36px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  transition: all 0.2s !important;
}

.scene-send-button:hover {
  background: rgba(33, 128, 141, 0.8) !important;
  border-color: rgba(33, 128, 141, 0.8) !important;
  color: white !important;
  transform: translateY(-1px);
  box-shadow: none !important;
}

.scene-send-button:focus {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-send-button:active {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
  transform: translateY(0);
  box-shadow: none !important;
}

.scene-send-button:disabled {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #bfbfbf !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.scene-send-button:disabled:hover {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #bfbfbf !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 角色简介Modal样式 */
.scene-profile-modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-profile-content {
  padding: 16px 0;
}

.scene-profile-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.scene-profile-info h4 {
  margin: 0;
}

.scene-profile-description {
  margin-top: 8px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* AI老师点评按钮样式 */
.scene-ai-comment-button {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  font-size: 16px !important;
  font-weight: 500 !important;
}

.scene-ai-comment-button:hover {
  background-color: rgba(33, 128, 141, 0.8) !important;
  border-color: rgba(33, 128, 141, 0.8) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.scene-ai-comment-button:focus {
  background-color: rgb(33, 128, 141) !important;
  border-color: rgb(33, 128, 141) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 重新练习按钮样式 */
.scene-retry-button {
  border: none;
  outline: none;
  box-shadow: none;
}

.scene-retry-button:disabled {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgb(255, 255, 255) !important;
  cursor: not-allowed;
  opacity: 0.8;
}

.scene-retry-button:disabled:hover {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgb(255, 255, 255) !important;
  opacity: 0.8;
}

/* React Icons 样式优化 */
.scene-function-button svg {
  width: 16px !important;
  height: 16px !important;
  transition: all 0.2s !important;
}

/* FontAwesome 图标特殊优化 */
.scene-function-button svg[data-icon] {
  font-size: 16px !important;
}

/* 确保图标垂直居中 */
.scene-function-button .anticon,
.scene-function-button svg {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 响应式图标尺寸 */
@media (max-width: 768px) {
  .scene-function-button svg {
    width: 14px !important;
    height: 14px !important;
  }
  
  .scene-send-button svg {
    width: 14px !important;
    height: 14px !important;
  }
}

@media (min-width: 1200px) {
  .scene-function-button svg {
    width: 18px !important;
    height: 18px !important;
  }
  
  .scene-send-button svg {
    width: 16px !important;
    height: 16px !important;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .scene-function-button svg,
  .scene-send-button svg {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 流式光标样式 */
.streaming-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: var(--primary-color);
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 删除按钮默认样式 */
.delete-button {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: white !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 删除按钮hover样式 */
.delete-button:hover {
  background-color: rgba(255, 77, 79, 0.8) !important;
  border-color: rgba(255, 77, 79, 0.8) !important;
  color: white !important;
  box-shadow: none !important;
}

/* 等待参会者发言状态样式 */
.scene-waiting-participants {
  position: absolute;
  bottom: 120px; /* 调整位置，避免被浮动输入框遮住 */
  left: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8); /* 磨砂半透明背景 */
  backdrop-filter: blur(10px); /* 磨砂效果 */
  -webkit-backdrop-filter: blur(10px); /* Safari兼容性 */
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2); /* 添加微妙边框增强磨砂效果 */
  font-size: 14px;
  color: var(--text-secondary);
  z-index: 150; /* 提高z-index，确保在浮动输入框之上 */
}

/* 发言中气泡样式 */
.scene-speaking-bubble {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

/* 加载点动画样式 */
.scene-loading-dots {
  display: flex;
  gap: 2px;
  align-items: center;
}

.scene-loading-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--primary-color);
  animation: loading-dots 1.4s infinite ease-in-out;
}

.scene-loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.scene-loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.scene-loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}